package logic

import (
	"context"
	"fmt"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model/mongdb"
	"app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/pat"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

// CheckUserRealName 检查用户是否已实名认证
func CheckUserRealName(ctx context.Context, userID string) error {
	if userID == "" {
		return define.CC500701Err.SetMsg("用户ID不能为空")
	}

	realInfos, err := pat.GetRealInfo(ctx, []string{userID})
	if err != nil {
		log.Ctx(ctx).Errorf("获取用户实名信息失败: %v", err)
		return define.CC500701Err.SetMsg("获取用户实名信息失败")
	}

	if len(realInfos) == 0 || realInfos[0].RealName == "" {
		return define.CC500701Err.SetMsg("请先完成实名认证")
	}

	return nil
}

// MaskAddress 脱敏收货地址信息
func MaskAddress(address *define.Address) *define.Address {
	if address == nil {
		return nil
	}

	return &define.Address{
		Name:        util.MaskReceiverName(address.Name),
		MobilePhone: util.MaskPhoneNumber(address.MobilePhone),
		Code:        address.Code,
		Area:        util.MaskArea(address.Area),
		Place:       util.MaskPlace(address.Place),
	}
}

// PushOrderNotification 推送订单相关通知
// 根据订单状态变化向相关用户推送通知
func PushOrderNotification(ctx context.Context, order *model.CardOrder, toUserID string) error {
	// 查询发送方用户昵称
	var fromUserID string
	if toUserID == order.BuyerID {
		// 推送给买家，发送方是卖家
		fromUserID = order.SellerID
	} else {
		// 推送给卖家，发送方是买家
		fromUserID = order.BuyerID
	}

	var fromUserNickname string
	if fromUser, err := facade.GetNodeUser(ctx, fromUserID); err == nil && fromUser != nil {
		fromUserNickname = fromUser.PatbgDetail.Nickname
	}
	if fromUserNickname == "" {
		fromUserNickname = "用户"
	}

	// 构造推送标题和内容
	var title string
	switch order.GetStatus() {
	case enums.OrderStatusUnPaid:
		title = "你有新的订单「待付款」"
	case enums.OrderStatusUnDelivered:
		title = "你有订单「待发货」"
	case enums.OrderStatusUnReceive:
		title = "你有订单「待收货」"
	case enums.OrderStatusCompleted:
		title = "交易完成"
	default:
		title = "订单状态更新"
	}
	content := "来自 " + fromUserNickname

	// 构造extras，包含跳转URL
	baseURL := "https://sit-wcjs.ahbq.com.cn"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		baseURL = "https://wcjs.ahbq.com.cn"
	}
	pageURL := fmt.Sprintf("ojb://webview_h5?url=%s/pages/Card/OrderPayment/index?id=%s", baseURL, order.ID)
	extras := map[string]interface{}{
		"url": pageURL,
	}

	// 构造relateInfo
	var relateScene notienums.PushRelateSceneEnum
	switch order.GetStatus() {
	case enums.OrderStatusUnPaid:
		relateScene = notienums.PushRelateSceneUnPaid
	case enums.OrderStatusUnDelivered:
		relateScene = notienums.PushRelateSceneUnDelivered
	case enums.OrderStatusUnReceive:
		relateScene = notienums.PushRelateSceneUnReceived
	case enums.OrderStatusCompleted:
		relateScene = notienums.PushRelateSceneCompleted
	default:
		relateScene = notienums.PushRelateSceneUnPaid // 默认场景
	}

	relateInfo := notidefine.PushRelateInfo{
		RelateType:  notienums.PushRelateTypeCardCommunityOrder,
		RelateScene: relateScene,
		RelateID:    order.ID,
	}

	// 构造推送消息参数
	message := notidefine.PushMessage{
		Title:         title,
		Content:       content,
		AudienceType:  notienums.PushAudienceTypeUser,
		UserIDs:       []string{toUserID},
		AndroidExtras: extras,
		IOSExtras:     extras,
	}

	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		log.Ctx(ctx).Errorf("订单推送通知发送失败 - 用户: %s, 错误: %v", toUserID, err)
		return nil // 推送失败不影响主业务流程
	}

	log.Ctx(ctx).Infof("订单推送通知发送成功 - 用户: %s, 订单: %s, 状态: %s, 推送总数: %d, 成功: %d, 失败: %d",
		toUserID, order.ID, order.GetStatus().String(), result.SendTotal, result.SuccessCount, result.FailCount)

	return nil
}

// ValidateOrderCreation 订单创建前置校验
// 包含买家用户有效性、卖家订单限制、会话存在性和状态校验
func ValidateOrderCreation(ctx context.Context, sellerID, buyerID string) (*mongdb.User, *model.Conversation, error) {
	// 检查是否向自己创建订单
	if buyerID == sellerID {
		return nil, nil, commondefine.ParamErr.SetMsg("不能向自己创建订单")
	}

	// 校验买家ID是否有效
	buyerUser, err := facade.GetNodeUser(ctx, buyerID)
	if err != nil || buyerUser == nil {
		log.Ctx(ctx).Errorf("获取买家信息失败: %v", err)
		return nil, nil, commondefine.ParamErr.SetMsg("买家用户不存在")
	}

	// 校验卖家待支付订单数量限制（不能超过5单）
	if err := CheckSellerUnpaidOrderLimit(ctx, sellerID); err != nil {
		return nil, nil, err
	}

	// 查找买家和卖家之间的现有会话
	conversation := FindExistingConversation(ctx, sellerID, buyerID)
	if conversation == nil {
		log.Ctx(ctx).Infof("用户 %s 和 %s 之间没有会话记录，不能创建订单", sellerID, buyerID)
		return nil, nil, define.CC500407Err.SetMsg("会话不存在，暂时无法创建订单")
	}

	// 验证会话状态是否正常
	if conversation.Status == -1 {
		log.Ctx(ctx).Infof("用户 %s 和 %s 之间的会话状态为限制中，不能创建订单", sellerID, buyerID)
		return nil, nil, define.CC500406Err.SetMsg("在 ta 回复前，你还不能给 ta 发送交易订单哦～")
	}

	return buyerUser, conversation, nil
}
