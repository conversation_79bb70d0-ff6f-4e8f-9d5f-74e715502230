// {
//     "go.useLanguageServer": true,
//     "go.languageServerExperimentalFeatures": {
//         "diagnostics": true,
//         "documentLink": true
//     },
//     "go.lintTool": "golangci-lint",
//     "go.lintFlags": [
//         "--fast"
//     ],
//     "go.vetOnSave": "package",
//     "go.buildOnSave": "package",
//     "go.testOnSave": false,
//     "go.coverOnSave": false,
//     "go.gocodeAutoBuild": false,
//     "go.useCodeSnippetsOnFunctionSuggest": true,
//     "go.inferGopath": true,
//     "go.gotoSymbol.includeImports": true,
//     "go.gotoSymbol.includeGoroot": true,
//     "go.docsTool": "godoc",
//     "go.formatTool": "goimports",
//     "go.alternateTools": {
//         "go": "go",
//         "gofmt": "goimports"
//     },
//     "gopls": {
//         "ui.completion.usePlaceholders": true,
//         "ui.diagnostic.staticcheck": true,
//         "ui.completion.completionBudget": "100ms",
//         "ui.completion.matcher": "Fuzzy",
//         "ui.navigation.importShortcut": "Both",
//         "ui.semanticTokens": true,
//         "ui.codelenses": {
//             "gc_details": false,
//             "generate": true,
//             "regenerate_cgo": true,
//             "test": true,
//             "tidy": true,
//             "upgrade_dependency": true,
//             "vendor": true
//         },
//         "build.directoryFilters": [
//             "-node_modules"
//         ],
//         "formatting.gofumpt": true,
//         "formatting.local": "app_service"
//     },
//     "files.exclude": {
//         "**/node_modules": true,
//         "**/.git": true,
//         "**/.DS_Store": true,
//         "**/logs": true
//     },
//     "search.exclude": {
//         "**/node_modules": true,
//         "**/logs": true,
//         "**/.git": true
//     }
// }
