package util

import (
	"encoding/base64"
	"encoding/json"
	"sort"
	"strconv"
	"strings"
)

// Format 格式化字符串
func Format(temp string, param map[string]string) string {
	if len(param) == 0 {
		return temp
	}
	for k, v := range param {
		if v != "" {
			old := "{" + k + "}"
			temp = strings.Replace(temp, old, v, -1)
		}
	}
	return temp
}

// Replace 替换字符串
func Replace(temp string) string {
	if strings.Contains(temp, "{") && strings.Contains(temp, "}") {
		temp = temp[strings.Index(temp, "{")+1 : strings.Index(temp, "}")]
		return temp
	}
	return temp
}

// StrVal 对象转字符串
func StrVal(value interface{}) string {
	var key string
	if value == nil {
		return key
	}

	switch value.(type) {
	case float64:
		ft := value.(float64)
		key = strconv.FormatFloat(ft, 'f', -1, 64)
	case float32:
		ft := value.(float32)
		key = strconv.FormatFloat(float64(ft), 'f', -1, 64)
	case int:
		it := value.(int)
		key = strconv.Itoa(it)
	case uint:
		it := value.(uint)
		key = strconv.Itoa(int(it))
	case int8:
		it := value.(int8)
		key = strconv.Itoa(int(it))
	case uint8:
		it := value.(uint8)
		key = strconv.Itoa(int(it))
	case int16:
		it := value.(int16)
		key = strconv.Itoa(int(it))
	case uint16:
		it := value.(uint16)
		key = strconv.Itoa(int(it))
	case int32:
		it := value.(int32)
		key = strconv.Itoa(int(it))
	case uint32:
		it := value.(uint32)
		key = strconv.Itoa(int(it))
	case int64:
		it := value.(int64)
		key = strconv.FormatInt(it, 10)
	case uint64:
		it := value.(uint64)
		key = strconv.FormatUint(it, 10)
	case string:
		key = value.(string)
	case []byte:
		key = string(value.([]byte))
	default:
		newValue, _ := json.Marshal(value)
		key = string(newValue)
	}

	return key
}

// In 字符串是否存在数组
func In(target string, strArray []string) bool {
	sort.Strings(strArray)
	index := sort.SearchStrings(strArray, target)
	if index < len(strArray) && strArray[index] == target {
		return true
	}
	return false
}

// PhoneMix 手机号码混淆
func PhoneMix(phone string) string {
	if phone != "" {
		phone = phone[:3] + "****" + phone[7:]
	}
	return phone
}

// MaskNickname 用户昵称脱敏
func MaskNickname(nickname string) string {
	if len(nickname) <= 2 {
		return nickname
	}

	// 转换为 rune 切片处理多字节字符（如中文）
	runes := []rune(nickname)
	length := len(runes)

	// 计算需要脱敏的星号数量
	prefix := 2
	lastChar := string(runes[length-1:])
	maskedCount := length - prefix - 1

	// 构建脱敏字符串
	return string(runes[:prefix]) +
		strings.Repeat("*", maskedCount) +
		lastChar
}

func HideString(origin string, start, end int, replaceChar string) string {
	runes := []rune(origin)
	size := len(runes)

	if start > size-1 || start < 0 || end < 0 || start > end {
		return origin
	}

	if end > size {
		end = size
	}

	if replaceChar == "" {
		return origin
	}

	startStr := string(runes[0:start])
	endStr := string(runes[end:size])

	replaceSize := end - start
	replaceStr := strings.Repeat(replaceChar, replaceSize)

	return startStr + replaceStr + endStr
}

// ConvertToBase64 Base64转化
func ConvertToBase64(input string) string {
	return base64.StdEncoding.EncodeToString([]byte(input))
}

// Str2Int64 将字符串转换为 int64 类型
func Str2Int64(s string) (int64, error) {
	// 尝试将字符串转换为 int64
	num, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0, err
	}
	return num, nil
}

// MaskReceiverName 脱敏收货人姓名：只显示姓氏
func MaskReceiverName(name string) string {
	if len(name) == 0 {
		return ""
	}
	runes := []rune(name)
	if len(runes) == 1 {
		return string(runes[0]) + "*"
	}
	return string(runes[0]) + "*"
}

// MaskPhoneNumber 脱敏手机号：133****1234
func MaskPhoneNumber(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MaskArea 脱敏区域信息：只保留到"区"
func MaskArea(area string) string {
	if area == "" {
		return ""
	}
	// 查找"区"字的位置
	if idx := strings.Index(area, "区"); idx != -1 {
		return area[:idx+len("区")]
	}
	// 如果没有"区"字，返回前几个字符
	runes := []rune(area)
	if len(runes) > 3 {
		return string(runes[:3]) + "..."
	}
	return area
}

// MaskPlace 脱敏详细地址：全模糊处理
func MaskPlace(place string) string {
	if place == "" {
		return ""
	}
	return "***"
}
